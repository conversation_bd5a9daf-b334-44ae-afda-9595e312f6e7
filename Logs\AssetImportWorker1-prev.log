Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.30f1 (62b05ba0686a) revision 6467675'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit ProfessionalWorkstation' Language: 'en' Physical Memory: 32684 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
D:/My Project/Driving Simulator Game Z TEC
-logFile
Logs/AssetImportWorker1.log
-srvPort
50207
-job-worker-count
11
-background-job-worker-count
8
-asset-garbage-collectors
1
Successfully changed project path to: D:/My Project/Driving Simulator Game Z TEC
D:/My Project/Driving Simulator Game Z TEC
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [22208]  Target information:

Player connection [22208]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 3063233649 [EditorId] 3063233649 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-GMG2SOO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [22208]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 3063233649 [EditorId] 3063233649 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-GMG2SOO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [22208] Host joined multi-casting on [***********:54997]...
Player connection [22208] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 11
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 149.80 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.30f1 (62b05ba0686a)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/My Project/Driving Simulator Game Z TEC/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.0]
    Renderer: NVIDIA Quadro K2200 (ID=0x13ba)
    Vendor:   NVIDIA
    VRAM:     4035 MB
    Driver:   10.18.13.5330
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56224
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.006966 seconds.
- Loaded All Assemblies, in  2.419 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 369 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.004 seconds
Domain Reload Profiling: 3419ms
	BeginReloadAssembly (590ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (2ms)
	RebuildCommonClasses (340ms)
	RebuildNativeTypeToScriptingClass (28ms)
	initialDomainReloadingComplete (108ms)
	LoadAllAssembliesAndSetupDomain (1348ms)
		LoadAssemblies (586ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1341ms)
			TypeCache.Refresh (1339ms)
				TypeCache.ScanAssembly (1312ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (1004ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (923ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (526ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (88ms)
			ProcessInitializeOnLoadAttributes (193ms)
			ProcessInitializeOnLoadMethodAttributes (109ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  6.376 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.17 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.314 seconds
Domain Reload Profiling: 7684ms
	BeginReloadAssembly (357ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (72ms)
	RebuildCommonClasses (73ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (51ms)
	LoadAllAssembliesAndSetupDomain (5864ms)
		LoadAssemblies (4993ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1076ms)
			TypeCache.Refresh (906ms)
				TypeCache.ScanAssembly (774ms)
			BuildScriptInfoCaches (142ms)
			ResolveRequiredComponents (23ms)
	FinalizeReload (1315ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1081ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (185ms)
			ProcessInitializeOnLoadAttributes (754ms)
			ProcessInitializeOnLoadMethodAttributes (105ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Launched and connected shader compiler UnityShaderCompiler.exe after 0.66 seconds
Refreshing native plugins compatible for Editor in 1.33 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 143 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6479 unused Assets / (5.9 MB). Loaded Objects now: 7061.
Memory consumption went from 247.0 MB to 241.1 MB.
Total: 13.687200 ms (FindLiveObjects: 1.051500 ms CreateObjectMapping: 0.801400 ms MarkObjects: 6.979000 ms  DeleteObjects: 4.851400 ms)

========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.457 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.28 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.184 seconds
Domain Reload Profiling: 2641ms
	BeginReloadAssembly (355ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (97ms)
	RebuildCommonClasses (67ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (52ms)
	LoadAllAssembliesAndSetupDomain (960ms)
		LoadAssemblies (674ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (466ms)
			TypeCache.Refresh (20ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (421ms)
			ResolveRequiredComponents (21ms)
	FinalizeReload (1185ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (957ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (22ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (173ms)
			ProcessInitializeOnLoadAttributes (656ms)
			ProcessInitializeOnLoadMethodAttributes (90ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Refreshing native plugins compatible for Editor in 1.73 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6477 unused Assets / (5.6 MB). Loaded Objects now: 7063.
Memory consumption went from 224.5 MB to 218.9 MB.
Total: 18.572900 ms (FindLiveObjects: 1.639700 ms CreateObjectMapping: 0.997200 ms MarkObjects: 10.959000 ms  DeleteObjects: 4.974400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.497 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.39 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.236 seconds
Domain Reload Profiling: 2733ms
	BeginReloadAssembly (355ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (88ms)
	RebuildCommonClasses (66ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (52ms)
	LoadAllAssembliesAndSetupDomain (1000ms)
		LoadAssemblies (714ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (479ms)
			TypeCache.Refresh (18ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (431ms)
			ResolveRequiredComponents (22ms)
	FinalizeReload (1237ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1003ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (21ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (190ms)
			ProcessInitializeOnLoadAttributes (666ms)
			ProcessInitializeOnLoadMethodAttributes (111ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Refreshing native plugins compatible for Editor in 1.46 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6477 unused Assets / (5.9 MB). Loaded Objects now: 7065.
Memory consumption went from 224.5 MB to 218.6 MB.
Total: 20.198800 ms (FindLiveObjects: 1.051400 ms CreateObjectMapping: 0.795200 ms MarkObjects: 11.305000 ms  DeleteObjects: 7.044600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.54 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6471 unused Assets / (5.9 MB). Loaded Objects now: 7066.
Memory consumption went from 224.7 MB to 218.9 MB.
Total: 16.138000 ms (FindLiveObjects: 1.152900 ms CreateObjectMapping: 0.902700 ms MarkObjects: 9.302400 ms  DeleteObjects: 4.777600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.557 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.58 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.459 seconds
Domain Reload Profiling: 3017ms
	BeginReloadAssembly (376ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (98ms)
	RebuildCommonClasses (69ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (54ms)
	LoadAllAssembliesAndSetupDomain (1034ms)
		LoadAssemblies (780ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (453ms)
			TypeCache.Refresh (31ms)
				TypeCache.ScanAssembly (8ms)
			BuildScriptInfoCaches (394ms)
			ResolveRequiredComponents (21ms)
	FinalizeReload (1460ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1125ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (24ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (224ms)
			ProcessInitializeOnLoadAttributes (749ms)
			ProcessInitializeOnLoadMethodAttributes (111ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.76 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6478 unused Assets / (5.9 MB). Loaded Objects now: 7068.
Memory consumption went from 224.6 MB to 218.7 MB.
Total: 22.905400 ms (FindLiveObjects: 1.288800 ms CreateObjectMapping: 0.894300 ms MarkObjects: 12.077200 ms  DeleteObjects: 8.642000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 2644.088198 seconds.
  path: Assets/Game Script/Vehiclespawnsystem/VehiclespawnAroundPlayer.cs
  artifactKey: Guid(c3eb1419e5a42e543be292270cf46b46) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Game Script/Vehiclespawnsystem/VehiclespawnAroundPlayer.cs using Guid(c3eb1419e5a42e543be292270cf46b46) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7e545146a7966644b1b6972f9a20bf1a') in 0.0558035 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.454 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.32 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.205 seconds
Domain Reload Profiling: 2660ms
	BeginReloadAssembly (356ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (90ms)
	RebuildCommonClasses (68ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (53ms)
	LoadAllAssembliesAndSetupDomain (952ms)
		LoadAssemblies (688ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (452ms)
			TypeCache.Refresh (26ms)
				TypeCache.ScanAssembly (8ms)
			BuildScriptInfoCaches (399ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1206ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (977ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (21ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (179ms)
			ProcessInitializeOnLoadAttributes (663ms)
			ProcessInitializeOnLoadMethodAttributes (96ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.74 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6478 unused Assets / (5.7 MB). Loaded Objects now: 7072.
Memory consumption went from 225.1 MB to 219.4 MB.
Total: 16.648300 ms (FindLiveObjects: 1.143300 ms CreateObjectMapping: 0.892000 ms MarkObjects: 9.796700 ms  DeleteObjects: 4.814700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.73 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6472 unused Assets / (5.9 MB). Loaded Objects now: 7073.
Memory consumption went from 225.3 MB to 219.5 MB.
Total: 18.541000 ms (FindLiveObjects: 1.856400 ms CreateObjectMapping: 0.865700 ms MarkObjects: 11.003600 ms  DeleteObjects: 4.813400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.474 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.27 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.280 seconds
Domain Reload Profiling: 2755ms
	BeginReloadAssembly (348ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (80ms)
	RebuildCommonClasses (69ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (54ms)
	LoadAllAssembliesAndSetupDomain (980ms)
		LoadAssemblies (708ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (465ms)
			TypeCache.Refresh (27ms)
				TypeCache.ScanAssembly (8ms)
			BuildScriptInfoCaches (412ms)
			ResolveRequiredComponents (21ms)
	FinalizeReload (1281ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1023ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (21ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (202ms)
			ProcessInitializeOnLoadAttributes (679ms)
			ProcessInitializeOnLoadMethodAttributes (105ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.58 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6481 unused Assets / (5.9 MB). Loaded Objects now: 7077.
Memory consumption went from 225.1 MB to 219.2 MB.
Total: 17.482600 ms (FindLiveObjects: 1.601500 ms CreateObjectMapping: 1.050000 ms MarkObjects: 9.450900 ms  DeleteObjects: 5.378700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.564 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.35 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.198 seconds
Domain Reload Profiling: 2764ms
	BeginReloadAssembly (351ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (93ms)
	RebuildCommonClasses (66ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (52ms)
	LoadAllAssembliesAndSetupDomain (1071ms)
		LoadAssemblies (697ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (547ms)
			TypeCache.Refresh (19ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (501ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1199ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (968ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (178ms)
			ProcessInitializeOnLoadAttributes (657ms)
			ProcessInitializeOnLoadMethodAttributes (95ms)
			AfterProcessingInitializeOnLoad (12ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.49 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6481 unused Assets / (5.8 MB). Loaded Objects now: 7079.
Memory consumption went from 225.1 MB to 219.4 MB.
Total: 17.531600 ms (FindLiveObjects: 1.100500 ms CreateObjectMapping: 0.818100 ms MarkObjects: 10.996500 ms  DeleteObjects: 4.613900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.471 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.25 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.226 seconds
Domain Reload Profiling: 2696ms
	BeginReloadAssembly (341ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (86ms)
	RebuildCommonClasses (66ms)
	RebuildNativeTypeToScriptingClass (25ms)
	initialDomainReloadingComplete (54ms)
	LoadAllAssembliesAndSetupDomain (983ms)
		LoadAssemblies (695ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (471ms)
			TypeCache.Refresh (28ms)
				TypeCache.ScanAssembly (7ms)
			BuildScriptInfoCaches (417ms)
			ResolveRequiredComponents (21ms)
	FinalizeReload (1227ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (989ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (186ms)
			ProcessInitializeOnLoadAttributes (669ms)
			ProcessInitializeOnLoadMethodAttributes (98ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.34 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6481 unused Assets / (5.8 MB). Loaded Objects now: 7081.
Memory consumption went from 225.1 MB to 219.3 MB.
Total: 15.868200 ms (FindLiveObjects: 1.210100 ms CreateObjectMapping: 0.883900 ms MarkObjects: 8.967000 ms  DeleteObjects: 4.804300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.448 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.30 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.233 seconds
Domain Reload Profiling: 2682ms
	BeginReloadAssembly (340ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (83ms)
	RebuildCommonClasses (67ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (54ms)
	LoadAllAssembliesAndSetupDomain (962ms)
		LoadAssemblies (679ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (462ms)
			TypeCache.Refresh (26ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (411ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1234ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (987ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (178ms)
			ProcessInitializeOnLoadAttributes (674ms)
			ProcessInitializeOnLoadMethodAttributes (98ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.53 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6481 unused Assets / (5.8 MB). Loaded Objects now: 7083.
Memory consumption went from 225.2 MB to 219.3 MB.
Total: 17.640700 ms (FindLiveObjects: 1.338700 ms CreateObjectMapping: 1.084900 ms MarkObjects: 10.362000 ms  DeleteObjects: 4.853400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.483 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.42 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.294 seconds
Domain Reload Profiling: 2778ms
	BeginReloadAssembly (350ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (92ms)
	RebuildCommonClasses (68ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (55ms)
	LoadAllAssembliesAndSetupDomain (986ms)
		LoadAssemblies (697ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (471ms)
			TypeCache.Refresh (33ms)
				TypeCache.ScanAssembly (7ms)
			BuildScriptInfoCaches (413ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1295ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1040ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (184ms)
			ProcessInitializeOnLoadAttributes (716ms)
			ProcessInitializeOnLoadMethodAttributes (104ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 2.33 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6481 unused Assets / (5.8 MB). Loaded Objects now: 7085.
Memory consumption went from 225.1 MB to 219.4 MB.
Total: 21.686900 ms (FindLiveObjects: 1.427900 ms CreateObjectMapping: 2.029200 ms MarkObjects: 11.879900 ms  DeleteObjects: 6.347600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1230.842261 seconds.
  path: Assets/MSK 2.2/Scripts/BikeUIController.cs
  artifactKey: Guid(c50a04fd0ad3fb141a10f4c6b8996c50) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/MSK 2.2/Scripts/BikeUIController.cs using Guid(c50a04fd0ad3fb141a10f4c6b8996c50) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '86e1e8d8661d8de570e7d3ff1bc9008a') in 0.0675715 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.547 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.31 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.192 seconds
Domain Reload Profiling: 2739ms
	BeginReloadAssembly (372ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (104ms)
	RebuildCommonClasses (64ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (56ms)
	LoadAllAssembliesAndSetupDomain (1031ms)
		LoadAssemblies (713ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (507ms)
			TypeCache.Refresh (24ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (454ms)
			ResolveRequiredComponents (21ms)
	FinalizeReload (1192ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (965ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (179ms)
			ProcessInitializeOnLoadAttributes (659ms)
			ProcessInitializeOnLoadMethodAttributes (92ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.41 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6481 unused Assets / (6.9 MB). Loaded Objects now: 7087.
Memory consumption went from 225.1 MB to 218.3 MB.
Total: 18.320100 ms (FindLiveObjects: 1.245300 ms CreateObjectMapping: 0.853600 ms MarkObjects: 9.333700 ms  DeleteObjects: 6.885500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.460 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.21 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.207 seconds
Domain Reload Profiling: 2667ms
	BeginReloadAssembly (346ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (95ms)
	RebuildCommonClasses (66ms)
	RebuildNativeTypeToScriptingClass (27ms)
	initialDomainReloadingComplete (52ms)
	LoadAllAssembliesAndSetupDomain (968ms)
		LoadAssemblies (681ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (465ms)
			TypeCache.Refresh (22ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (418ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1208ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (978ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (183ms)
			ProcessInitializeOnLoadAttributes (666ms)
			ProcessInitializeOnLoadMethodAttributes (93ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.45 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6481 unused Assets / (5.8 MB). Loaded Objects now: 7089.
Memory consumption went from 225.2 MB to 219.4 MB.
Total: 15.036600 ms (FindLiveObjects: 1.104300 ms CreateObjectMapping: 0.837400 ms MarkObjects: 7.768600 ms  DeleteObjects: 5.323900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.453 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.31 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.201 seconds
Domain Reload Profiling: 2655ms
	BeginReloadAssembly (357ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (100ms)
	RebuildCommonClasses (71ms)
	RebuildNativeTypeToScriptingClass (26ms)
	initialDomainReloadingComplete (51ms)
	LoadAllAssembliesAndSetupDomain (947ms)
		LoadAssemblies (694ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (437ms)
			TypeCache.Refresh (20ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (389ms)
			ResolveRequiredComponents (21ms)
	FinalizeReload (1202ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (970ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (181ms)
			ProcessInitializeOnLoadAttributes (660ms)
			ProcessInitializeOnLoadMethodAttributes (94ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.68 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6481 unused Assets / (5.8 MB). Loaded Objects now: 7091.
Memory consumption went from 225.2 MB to 219.3 MB.
Total: 17.506200 ms (FindLiveObjects: 1.476000 ms CreateObjectMapping: 0.965700 ms MarkObjects: 10.125700 ms  DeleteObjects: 4.937000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.468 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.31 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.280 seconds
Domain Reload Profiling: 2749ms
	BeginReloadAssembly (348ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (93ms)
	RebuildCommonClasses (67ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (51ms)
	LoadAllAssembliesAndSetupDomain (977ms)
		LoadAssemblies (698ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (461ms)
			TypeCache.Refresh (44ms)
				TypeCache.ScanAssembly (14ms)
			BuildScriptInfoCaches (390ms)
			ResolveRequiredComponents (21ms)
	FinalizeReload (1281ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1006ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (23ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (190ms)
			ProcessInitializeOnLoadAttributes (674ms)
			ProcessInitializeOnLoadMethodAttributes (101ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 2.02 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6482 unused Assets / (5.7 MB). Loaded Objects now: 7094.
Memory consumption went from 225.2 MB to 219.4 MB.
Total: 21.528500 ms (FindLiveObjects: 1.848600 ms CreateObjectMapping: 3.441200 ms MarkObjects: 11.334100 ms  DeleteObjects: 4.902800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.429 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.39 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.192 seconds
Domain Reload Profiling: 2622ms
	BeginReloadAssembly (352ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (92ms)
	RebuildCommonClasses (62ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (50ms)
	LoadAllAssembliesAndSetupDomain (940ms)
		LoadAssemblies (671ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (450ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (408ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1193ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (963ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (182ms)
			ProcessInitializeOnLoadAttributes (652ms)
			ProcessInitializeOnLoadMethodAttributes (95ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.63 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6482 unused Assets / (5.7 MB). Loaded Objects now: 7096.
Memory consumption went from 225.2 MB to 219.5 MB.
Total: 24.935100 ms (FindLiveObjects: 1.443000 ms CreateObjectMapping: 1.470200 ms MarkObjects: 14.030400 ms  DeleteObjects: 7.988000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.39 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6477 unused Assets / (5.7 MB). Loaded Objects now: 7098.
Memory consumption went from 225.3 MB to 219.6 MB.
Total: 19.402700 ms (FindLiveObjects: 1.098000 ms CreateObjectMapping: 0.820600 ms MarkObjects: 11.235600 ms  DeleteObjects: 6.246600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.87 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6477 unused Assets / (5.8 MB). Loaded Objects now: 7098.
Memory consumption went from 225.3 MB to 219.5 MB.
Total: 18.477000 ms (FindLiveObjects: 1.923600 ms CreateObjectMapping: 1.071500 ms MarkObjects: 10.006700 ms  DeleteObjects: 5.473500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.457 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.27 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.204 seconds
Domain Reload Profiling: 2661ms
	BeginReloadAssembly (329ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (76ms)
	RebuildCommonClasses (68ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (54ms)
	LoadAllAssembliesAndSetupDomain (981ms)
		LoadAssemblies (692ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (471ms)
			TypeCache.Refresh (29ms)
				TypeCache.ScanAssembly (8ms)
			BuildScriptInfoCaches (404ms)
			ResolveRequiredComponents (30ms)
	FinalizeReload (1205ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (971ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (180ms)
			ProcessInitializeOnLoadAttributes (652ms)
			ProcessInitializeOnLoadMethodAttributes (101ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.76 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6484 unused Assets / (5.7 MB). Loaded Objects now: 7100.
Memory consumption went from 225.2 MB to 219.4 MB.
Total: 21.645800 ms (FindLiveObjects: 1.490000 ms CreateObjectMapping: 1.478000 ms MarkObjects: 11.237900 ms  DeleteObjects: 7.437300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.426 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.25 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.202 seconds
Domain Reload Profiling: 2628ms
	BeginReloadAssembly (340ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (82ms)
	RebuildCommonClasses (67ms)
	RebuildNativeTypeToScriptingClass (25ms)
	initialDomainReloadingComplete (52ms)
	LoadAllAssembliesAndSetupDomain (942ms)
		LoadAssemblies (691ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (423ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (382ms)
			ResolveRequiredComponents (21ms)
	FinalizeReload (1203ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (973ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (175ms)
			ProcessInitializeOnLoadAttributes (667ms)
			ProcessInitializeOnLoadMethodAttributes (95ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.31 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6484 unused Assets / (5.8 MB). Loaded Objects now: 7102.
Memory consumption went from 225.2 MB to 219.4 MB.
Total: 15.860800 ms (FindLiveObjects: 1.228100 ms CreateObjectMapping: 0.957000 ms MarkObjects: 9.252000 ms  DeleteObjects: 4.421700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.36 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6479 unused Assets / (5.8 MB). Loaded Objects now: 7104.
Memory consumption went from 225.4 MB to 219.5 MB.
Total: 14.862100 ms (FindLiveObjects: 1.110200 ms CreateObjectMapping: 0.875800 ms MarkObjects: 8.463000 ms  DeleteObjects: 4.410900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.53 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6479 unused Assets / (5.8 MB). Loaded Objects now: 7104.
Memory consumption went from 225.4 MB to 219.6 MB.
Total: 14.232200 ms (FindLiveObjects: 1.080100 ms CreateObjectMapping: 0.857000 ms MarkObjects: 6.591500 ms  DeleteObjects: 5.702500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.87 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6479 unused Assets / (5.8 MB). Loaded Objects now: 7104.
Memory consumption went from 225.4 MB to 219.6 MB.
Total: 18.627700 ms (FindLiveObjects: 1.282600 ms CreateObjectMapping: 1.079900 ms MarkObjects: 11.583900 ms  DeleteObjects: 4.679400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.81 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6479 unused Assets / (5.8 MB). Loaded Objects now: 7104.
Memory consumption went from 225.4 MB to 219.5 MB.
Total: 18.834000 ms (FindLiveObjects: 1.611900 ms CreateObjectMapping: 1.061100 ms MarkObjects: 11.306800 ms  DeleteObjects: 4.851600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.445 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.41 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.214 seconds
Domain Reload Profiling: 2659ms
	BeginReloadAssembly (340ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (82ms)
	RebuildCommonClasses (67ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (52ms)
	LoadAllAssembliesAndSetupDomain (962ms)
		LoadAssemblies (681ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (467ms)
			TypeCache.Refresh (27ms)
				TypeCache.ScanAssembly (8ms)
			BuildScriptInfoCaches (412ms)
			ResolveRequiredComponents (21ms)
	FinalizeReload (1215ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (980ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (180ms)
			ProcessInitializeOnLoadAttributes (660ms)
			ProcessInitializeOnLoadMethodAttributes (103ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.79 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6486 unused Assets / (5.8 MB). Loaded Objects now: 7106.
Memory consumption went from 225.2 MB to 219.4 MB.
Total: 19.082300 ms (FindLiveObjects: 1.389200 ms CreateObjectMapping: 0.884800 ms MarkObjects: 10.953700 ms  DeleteObjects: 5.852100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.455 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.49 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.289 seconds
Domain Reload Profiling: 2745ms
	BeginReloadAssembly (331ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (76ms)
	RebuildCommonClasses (71ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (53ms)
	LoadAllAssembliesAndSetupDomain (976ms)
		LoadAssemblies (682ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (477ms)
			TypeCache.Refresh (26ms)
				TypeCache.ScanAssembly (8ms)
			BuildScriptInfoCaches (422ms)
			ResolveRequiredComponents (21ms)
	FinalizeReload (1290ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1036ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (25ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (204ms)
			ProcessInitializeOnLoadAttributes (682ms)
			ProcessInitializeOnLoadMethodAttributes (109ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.96 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6481 unused Assets / (6.1 MB). Loaded Objects now: 7103.
Memory consumption went from 225.2 MB to 219.1 MB.
Total: 27.371800 ms (FindLiveObjects: 2.115100 ms CreateObjectMapping: 1.888700 ms MarkObjects: 14.591000 ms  DeleteObjects: 8.775000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.66 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6474 unused Assets / (5.9 MB). Loaded Objects now: 7103.
Memory consumption went from 225.4 MB to 219.5 MB.
Total: 19.375300 ms (FindLiveObjects: 1.379600 ms CreateObjectMapping: 0.915600 ms MarkObjects: 11.975300 ms  DeleteObjects: 5.102600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.430 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.35 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.229 seconds
Domain Reload Profiling: 2660ms
	BeginReloadAssembly (330ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (78ms)
	RebuildCommonClasses (67ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (53ms)
	LoadAllAssembliesAndSetupDomain (957ms)
		LoadAssemblies (665ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (471ms)
			TypeCache.Refresh (31ms)
				TypeCache.ScanAssembly (8ms)
			BuildScriptInfoCaches (413ms)
			ResolveRequiredComponents (21ms)
	FinalizeReload (1230ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (996ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (178ms)
			ProcessInitializeOnLoadAttributes (682ms)
			ProcessInitializeOnLoadMethodAttributes (99ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.82 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6481 unused Assets / (5.8 MB). Loaded Objects now: 7105.
Memory consumption went from 225.2 MB to 219.4 MB.
Total: 18.286400 ms (FindLiveObjects: 1.374900 ms CreateObjectMapping: 0.843200 ms MarkObjects: 10.792400 ms  DeleteObjects: 5.273800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.428 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.26 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.201 seconds
Domain Reload Profiling: 2630ms
	BeginReloadAssembly (335ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (79ms)
	RebuildCommonClasses (68ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (53ms)
	LoadAllAssembliesAndSetupDomain (949ms)
		LoadAssemblies (678ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (453ms)
			TypeCache.Refresh (22ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (404ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1202ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (970ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (179ms)
			ProcessInitializeOnLoadAttributes (652ms)
			ProcessInitializeOnLoadMethodAttributes (102ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.61 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6481 unused Assets / (5.8 MB). Loaded Objects now: 7107.
Memory consumption went from 225.2 MB to 219.4 MB.
Total: 14.345900 ms (FindLiveObjects: 1.058600 ms CreateObjectMapping: 0.903600 ms MarkObjects: 8.073800 ms  DeleteObjects: 4.308000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.474 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.19 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.198 seconds
Domain Reload Profiling: 2672ms
	BeginReloadAssembly (358ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (104ms)
	RebuildCommonClasses (68ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (52ms)
	LoadAllAssembliesAndSetupDomain (971ms)
		LoadAssemblies (704ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (447ms)
			TypeCache.Refresh (20ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (400ms)
			ResolveRequiredComponents (21ms)
	FinalizeReload (1199ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (972ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (182ms)
			ProcessInitializeOnLoadAttributes (659ms)
			ProcessInitializeOnLoadMethodAttributes (95ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.38 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6481 unused Assets / (5.8 MB). Loaded Objects now: 7109.
Memory consumption went from 225.2 MB to 219.5 MB.
Total: 16.408200 ms (FindLiveObjects: 1.639600 ms CreateObjectMapping: 0.882200 ms MarkObjects: 9.263100 ms  DeleteObjects: 4.621300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.443 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.28 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.205 seconds
Domain Reload Profiling: 2649ms
	BeginReloadAssembly (346ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (86ms)
	RebuildCommonClasses (66ms)
	RebuildNativeTypeToScriptingClass (27ms)
	initialDomainReloadingComplete (53ms)
	LoadAllAssembliesAndSetupDomain (950ms)
		LoadAssemblies (696ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (428ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (388ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1206ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (976ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (188ms)
			ProcessInitializeOnLoadAttributes (655ms)
			ProcessInitializeOnLoadMethodAttributes (97ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.76 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6481 unused Assets / (5.5 MB). Loaded Objects now: 7111.
Memory consumption went from 225.2 MB to 219.7 MB.
Total: 19.483900 ms (FindLiveObjects: 1.153400 ms CreateObjectMapping: 0.878200 ms MarkObjects: 10.957800 ms  DeleteObjects: 6.492900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.409 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.27 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.202 seconds
Domain Reload Profiling: 2612ms
	BeginReloadAssembly (344ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (80ms)
	RebuildCommonClasses (66ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (51ms)
	LoadAllAssembliesAndSetupDomain (924ms)
		LoadAssemblies (690ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (420ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (380ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1203ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (968ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (179ms)
			ProcessInitializeOnLoadAttributes (648ms)
			ProcessInitializeOnLoadMethodAttributes (100ms)
			AfterProcessingInitializeOnLoad (14ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.28 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6481 unused Assets / (5.8 MB). Loaded Objects now: 7113.
Memory consumption went from 225.2 MB to 219.4 MB.
Total: 13.664800 ms (FindLiveObjects: 1.073400 ms CreateObjectMapping: 0.837600 ms MarkObjects: 6.963800 ms  DeleteObjects: 4.788400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.422 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.30 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.232 seconds
Domain Reload Profiling: 2655ms
	BeginReloadAssembly (333ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (82ms)
	RebuildCommonClasses (67ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (54ms)
	LoadAllAssembliesAndSetupDomain (944ms)
		LoadAssemblies (664ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (460ms)
			TypeCache.Refresh (26ms)
				TypeCache.ScanAssembly (8ms)
			BuildScriptInfoCaches (407ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1232ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (988ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (188ms)
			ProcessInitializeOnLoadAttributes (667ms)
			ProcessInitializeOnLoadMethodAttributes (98ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 2.17 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6481 unused Assets / (6.0 MB). Loaded Objects now: 7115.
Memory consumption went from 225.2 MB to 219.3 MB.
Total: 20.928600 ms (FindLiveObjects: 2.309400 ms CreateObjectMapping: 1.249100 ms MarkObjects: 12.401200 ms  DeleteObjects: 4.965300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.420 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.31 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.226 seconds
Domain Reload Profiling: 2647ms
	BeginReloadAssembly (327ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (78ms)
	RebuildCommonClasses (68ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (52ms)
	LoadAllAssembliesAndSetupDomain (948ms)
		LoadAssemblies (668ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (459ms)
			TypeCache.Refresh (27ms)
				TypeCache.ScanAssembly (7ms)
			BuildScriptInfoCaches (405ms)
			ResolveRequiredComponents (22ms)
	FinalizeReload (1228ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (988ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (182ms)
			ProcessInitializeOnLoadAttributes (673ms)
			ProcessInitializeOnLoadMethodAttributes (97ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.53 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6481 unused Assets / (5.9 MB). Loaded Objects now: 7117.
Memory consumption went from 225.2 MB to 219.4 MB.
Total: 14.971400 ms (FindLiveObjects: 1.122900 ms CreateObjectMapping: 0.823000 ms MarkObjects: 8.260800 ms  DeleteObjects: 4.763600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.394 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.30 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.179 seconds
Domain Reload Profiling: 2576ms
	BeginReloadAssembly (333ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (88ms)
	RebuildCommonClasses (66ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (51ms)
	LoadAllAssembliesAndSetupDomain (921ms)
		LoadAssemblies (668ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (421ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (381ms)
			ResolveRequiredComponents (21ms)
	FinalizeReload (1180ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (949ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (173ms)
			ProcessInitializeOnLoadAttributes (644ms)
			ProcessInitializeOnLoadMethodAttributes (95ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.82 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6481 unused Assets / (5.9 MB). Loaded Objects now: 7119.
Memory consumption went from 225.2 MB to 219.3 MB.
Total: 19.252700 ms (FindLiveObjects: 1.407400 ms CreateObjectMapping: 0.846600 ms MarkObjects: 10.177000 ms  DeleteObjects: 6.819800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.68 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6474 unused Assets / (5.8 MB). Loaded Objects now: 7119.
Memory consumption went from 225.4 MB to 219.6 MB.
Total: 14.491300 ms (FindLiveObjects: 1.197200 ms CreateObjectMapping: 0.904000 ms MarkObjects: 7.861200 ms  DeleteObjects: 4.527600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.413 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.38 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.221 seconds
Domain Reload Profiling: 2635ms
	BeginReloadAssembly (323ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (77ms)
	RebuildCommonClasses (66ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (53ms)
	LoadAllAssembliesAndSetupDomain (947ms)
		LoadAssemblies (662ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (458ms)
			TypeCache.Refresh (29ms)
				TypeCache.ScanAssembly (8ms)
			BuildScriptInfoCaches (402ms)
			ResolveRequiredComponents (21ms)
	FinalizeReload (1222ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (985ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (176ms)
			ProcessInitializeOnLoadAttributes (661ms)
			ProcessInitializeOnLoadMethodAttributes (112ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 2.59 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6481 unused Assets / (5.9 MB). Loaded Objects now: 7121.
Memory consumption went from 225.2 MB to 219.3 MB.
Total: 20.367700 ms (FindLiveObjects: 2.251600 ms CreateObjectMapping: 1.855000 ms MarkObjects: 10.948500 ms  DeleteObjects: 5.304400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.457 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.35 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.209 seconds
Domain Reload Profiling: 2665ms
	BeginReloadAssembly (326ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (77ms)
	RebuildCommonClasses (68ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (55ms)
	LoadAllAssembliesAndSetupDomain (983ms)
		LoadAssemblies (689ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (471ms)
			TypeCache.Refresh (26ms)
				TypeCache.ScanAssembly (8ms)
			BuildScriptInfoCaches (408ms)
			ResolveRequiredComponents (31ms)
	FinalizeReload (1209ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (977ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (182ms)
			ProcessInitializeOnLoadAttributes (662ms)
			ProcessInitializeOnLoadMethodAttributes (98ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.72 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6481 unused Assets / (5.8 MB). Loaded Objects now: 7123.
Memory consumption went from 225.2 MB to 219.4 MB.
Total: 17.168300 ms (FindLiveObjects: 1.650600 ms CreateObjectMapping: 0.981900 ms MarkObjects: 9.727500 ms  DeleteObjects: 4.806000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.445 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.41 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.230 seconds
Domain Reload Profiling: 2675ms
	BeginReloadAssembly (342ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (79ms)
	RebuildCommonClasses (67ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (52ms)
	LoadAllAssembliesAndSetupDomain (959ms)
		LoadAssemblies (673ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (470ms)
			TypeCache.Refresh (26ms)
				TypeCache.ScanAssembly (8ms)
			BuildScriptInfoCaches (409ms)
			ResolveRequiredComponents (30ms)
	FinalizeReload (1231ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (992ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (181ms)
			ProcessInitializeOnLoadAttributes (674ms)
			ProcessInitializeOnLoadMethodAttributes (101ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.58 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6481 unused Assets / (6.8 MB). Loaded Objects now: 7125.
Memory consumption went from 225.2 MB to 218.4 MB.
Total: 24.268500 ms (FindLiveObjects: 1.134900 ms CreateObjectMapping: 1.058000 ms MarkObjects: 13.370700 ms  DeleteObjects: 8.702600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.465 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.30 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.219 seconds
Domain Reload Profiling: 2683ms
	BeginReloadAssembly (344ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (78ms)
	RebuildCommonClasses (73ms)
	RebuildNativeTypeToScriptingClass (27ms)
	initialDomainReloadingComplete (60ms)
	LoadAllAssembliesAndSetupDomain (959ms)
		LoadAssemblies (700ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (438ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (396ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1219ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (987ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (21ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (182ms)
			ProcessInitializeOnLoadAttributes (670ms)
			ProcessInitializeOnLoadMethodAttributes (99ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.33 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6481 unused Assets / (5.8 MB). Loaded Objects now: 7127.
Memory consumption went from 225.2 MB to 219.5 MB.
Total: 13.570400 ms (FindLiveObjects: 1.063500 ms CreateObjectMapping: 0.878400 ms MarkObjects: 7.170500 ms  DeleteObjects: 4.456100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.426 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.34 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.227 seconds
Domain Reload Profiling: 2653ms
	BeginReloadAssembly (335ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (86ms)
	RebuildCommonClasses (68ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (52ms)
	LoadAllAssembliesAndSetupDomain (946ms)
		LoadAssemblies (692ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (431ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (391ms)
			ResolveRequiredComponents (21ms)
	FinalizeReload (1228ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (999ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (175ms)
			ProcessInitializeOnLoadAttributes (684ms)
			ProcessInitializeOnLoadMethodAttributes (102ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.48 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6481 unused Assets / (5.9 MB). Loaded Objects now: 7129.
Memory consumption went from 225.2 MB to 219.4 MB.
Total: 13.428600 ms (FindLiveObjects: 1.080600 ms CreateObjectMapping: 0.836800 ms MarkObjects: 6.729900 ms  DeleteObjects: 4.779900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.446 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.24 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.300 seconds
Domain Reload Profiling: 2746ms
	BeginReloadAssembly (333ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (79ms)
	RebuildCommonClasses (67ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (57ms)
	LoadAllAssembliesAndSetupDomain (965ms)
		LoadAssemblies (687ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (459ms)
			TypeCache.Refresh (33ms)
				TypeCache.ScanAssembly (9ms)
			BuildScriptInfoCaches (397ms)
			ResolveRequiredComponents (21ms)
	FinalizeReload (1301ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1066ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (177ms)
			ProcessInitializeOnLoadAttributes (714ms)
			ProcessInitializeOnLoadMethodAttributes (139ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.35 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6481 unused Assets / (5.9 MB). Loaded Objects now: 7131.
Memory consumption went from 225.2 MB to 219.4 MB.
Total: 17.318300 ms (FindLiveObjects: 1.371700 ms CreateObjectMapping: 0.839100 ms MarkObjects: 10.146600 ms  DeleteObjects: 4.958500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.408 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.35 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.193 seconds
Domain Reload Profiling: 2601ms
	BeginReloadAssembly (339ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (88ms)
	RebuildCommonClasses (67ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (52ms)
	LoadAllAssembliesAndSetupDomain (925ms)
		LoadAssemblies (679ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (422ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (382ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1194ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (965ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (177ms)
			ProcessInitializeOnLoadAttributes (652ms)
			ProcessInitializeOnLoadMethodAttributes (99ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.46 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6481 unused Assets / (5.8 MB). Loaded Objects now: 7133.
Memory consumption went from 225.3 MB to 219.5 MB.
Total: 15.034500 ms (FindLiveObjects: 1.061900 ms CreateObjectMapping: 0.837600 ms MarkObjects: 8.836400 ms  DeleteObjects: 4.297300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.427 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.21 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.212 seconds
Domain Reload Profiling: 2640ms
	BeginReloadAssembly (338ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (82ms)
	RebuildCommonClasses (69ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (54ms)
	LoadAllAssembliesAndSetupDomain (942ms)
		LoadAssemblies (671ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (448ms)
			TypeCache.Refresh (41ms)
				TypeCache.ScanAssembly (11ms)
			BuildScriptInfoCaches (381ms)
			ResolveRequiredComponents (21ms)
	FinalizeReload (1213ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (971ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (180ms)
			ProcessInitializeOnLoadAttributes (659ms)
			ProcessInitializeOnLoadMethodAttributes (96ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.36 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6481 unused Assets / (5.9 MB). Loaded Objects now: 7135.
Memory consumption went from 225.2 MB to 219.4 MB.
Total: 15.496200 ms (FindLiveObjects: 1.076800 ms CreateObjectMapping: 0.853500 ms MarkObjects: 8.867100 ms  DeleteObjects: 4.697400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.408 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.39 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.231 seconds
Domain Reload Profiling: 2639ms
	BeginReloadAssembly (331ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (85ms)
	RebuildCommonClasses (66ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (52ms)
	LoadAllAssembliesAndSetupDomain (935ms)
		LoadAssemblies (675ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (432ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (391ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1232ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (981ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (171ms)
			ProcessInitializeOnLoadAttributes (676ms)
			ProcessInitializeOnLoadMethodAttributes (95ms)
			AfterProcessingInitializeOnLoad (13ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (19ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.73 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6481 unused Assets / (5.9 MB). Loaded Objects now: 7137.
Memory consumption went from 227.2 MB to 221.3 MB.
Total: 17.495700 ms (FindLiveObjects: 1.318400 ms CreateObjectMapping: 0.922300 ms MarkObjects: 10.901300 ms  DeleteObjects: 4.351600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.460 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.33 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.210 seconds
Domain Reload Profiling: 2671ms
	BeginReloadAssembly (341ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (79ms)
	RebuildCommonClasses (67ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (52ms)
	LoadAllAssembliesAndSetupDomain (976ms)
		LoadAssemblies (684ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (473ms)
			TypeCache.Refresh (19ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (429ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1211ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (981ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (185ms)
			ProcessInitializeOnLoadAttributes (664ms)
			ProcessInitializeOnLoadMethodAttributes (96ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 2.36 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6481 unused Assets / (5.8 MB). Loaded Objects now: 7139.
Memory consumption went from 227.2 MB to 221.3 MB.
Total: 18.636600 ms (FindLiveObjects: 1.148200 ms CreateObjectMapping: 1.030300 ms MarkObjects: 11.478900 ms  DeleteObjects: 4.977600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.455 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.28 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.243 seconds
Domain Reload Profiling: 2699ms
	BeginReloadAssembly (331ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (82ms)
	RebuildCommonClasses (66ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (55ms)
	LoadAllAssembliesAndSetupDomain (979ms)
		LoadAssemblies (690ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (463ms)
			TypeCache.Refresh (39ms)
				TypeCache.ScanAssembly (13ms)
			BuildScriptInfoCaches (387ms)
			ResolveRequiredComponents (31ms)
	FinalizeReload (1244ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1007ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (22ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (189ms)
			ProcessInitializeOnLoadAttributes (683ms)
			ProcessInitializeOnLoadMethodAttributes (97ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.98 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6481 unused Assets / (5.8 MB). Loaded Objects now: 7141.
Memory consumption went from 227.2 MB to 221.4 MB.
Total: 17.041800 ms (FindLiveObjects: 1.353100 ms CreateObjectMapping: 1.676800 ms MarkObjects: 9.353800 ms  DeleteObjects: 4.655000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.443 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.45 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.200 seconds
Domain Reload Profiling: 2644ms
	BeginReloadAssembly (332ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (80ms)
	RebuildCommonClasses (67ms)
	RebuildNativeTypeToScriptingClass (25ms)
	initialDomainReloadingComplete (52ms)
	LoadAllAssembliesAndSetupDomain (966ms)
		LoadAssemblies (694ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (449ms)
			TypeCache.Refresh (19ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (405ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1201ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (971ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (181ms)
			ProcessInitializeOnLoadAttributes (655ms)
			ProcessInitializeOnLoadMethodAttributes (99ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.43 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6481 unused Assets / (5.8 MB). Loaded Objects now: 7143.
Memory consumption went from 227.2 MB to 221.4 MB.
Total: 13.757900 ms (FindLiveObjects: 1.349500 ms CreateObjectMapping: 0.854800 ms MarkObjects: 7.213300 ms  DeleteObjects: 4.338700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.455 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.32 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.203 seconds
Domain Reload Profiling: 2657ms
	BeginReloadAssembly (346ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (86ms)
	RebuildCommonClasses (75ms)
	RebuildNativeTypeToScriptingClass (27ms)
	initialDomainReloadingComplete (58ms)
	LoadAllAssembliesAndSetupDomain (948ms)
		LoadAssemblies (705ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (430ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (389ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1203ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (974ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (176ms)
			ProcessInitializeOnLoadAttributes (662ms)
			ProcessInitializeOnLoadMethodAttributes (101ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.72 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6481 unused Assets / (6.0 MB). Loaded Objects now: 7145.
Memory consumption went from 227.2 MB to 221.2 MB.
Total: 20.650700 ms (FindLiveObjects: 2.958100 ms CreateObjectMapping: 1.130300 ms MarkObjects: 9.886800 ms  DeleteObjects: 6.673700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.477 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.30 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.226 seconds
Domain Reload Profiling: 2702ms
	BeginReloadAssembly (343ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (81ms)
	RebuildCommonClasses (66ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (55ms)
	LoadAllAssembliesAndSetupDomain (987ms)
		LoadAssemblies (701ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (467ms)
			TypeCache.Refresh (44ms)
				TypeCache.ScanAssembly (14ms)
			BuildScriptInfoCaches (397ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1228ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (996ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (181ms)
			ProcessInitializeOnLoadAttributes (680ms)
			ProcessInitializeOnLoadMethodAttributes (99ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.71 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6481 unused Assets / (5.8 MB). Loaded Objects now: 7147.
Memory consumption went from 227.2 MB to 221.3 MB.
Total: 15.056600 ms (FindLiveObjects: 1.088900 ms CreateObjectMapping: 0.922200 ms MarkObjects: 8.073900 ms  DeleteObjects: 4.969600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.451 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.31 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.203 seconds
Domain Reload Profiling: 2653ms
	BeginReloadAssembly (342ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (86ms)
	RebuildCommonClasses (67ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (55ms)
	LoadAllAssembliesAndSetupDomain (962ms)
		LoadAssemblies (712ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (431ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (387ms)
			ResolveRequiredComponents (21ms)
	FinalizeReload (1204ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (971ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (177ms)
			ProcessInitializeOnLoadAttributes (654ms)
			ProcessInitializeOnLoadMethodAttributes (101ms)
			AfterProcessingInitializeOnLoad (12ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.53 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6481 unused Assets / (5.8 MB). Loaded Objects now: 7149.
Memory consumption went from 227.2 MB to 221.4 MB.
Total: 13.961100 ms (FindLiveObjects: 1.262600 ms CreateObjectMapping: 0.861300 ms MarkObjects: 6.922300 ms  DeleteObjects: 4.912000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.459 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.30 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.232 seconds
Domain Reload Profiling: 2691ms
	BeginReloadAssembly (333ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (78ms)
	RebuildCommonClasses (65ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (58ms)
	LoadAllAssembliesAndSetupDomain (978ms)
		LoadAssemblies (716ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (437ms)
			TypeCache.Refresh (27ms)
				TypeCache.ScanAssembly (8ms)
			BuildScriptInfoCaches (384ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1233ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (993ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (191ms)
			ProcessInitializeOnLoadAttributes (666ms)
			ProcessInitializeOnLoadMethodAttributes (100ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.67 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6481 unused Assets / (6.2 MB). Loaded Objects now: 7151.
Memory consumption went from 227.2 MB to 221.0 MB.
Total: 19.703300 ms (FindLiveObjects: 1.260200 ms CreateObjectMapping: 0.897900 ms MarkObjects: 10.648300 ms  DeleteObjects: 6.894000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.447 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.30 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.232 seconds
Domain Reload Profiling: 2679ms
	BeginReloadAssembly (366ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (81ms)
	RebuildCommonClasses (71ms)
	RebuildNativeTypeToScriptingClass (27ms)
	initialDomainReloadingComplete (56ms)
	LoadAllAssembliesAndSetupDomain (927ms)
		LoadAssemblies (693ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (425ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (384ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1232ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1002ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (179ms)
			ProcessInitializeOnLoadAttributes (690ms)
			ProcessInitializeOnLoadMethodAttributes (95ms)
			AfterProcessingInitializeOnLoad (11ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.36 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6481 unused Assets / (5.8 MB). Loaded Objects now: 7153.
Memory consumption went from 227.2 MB to 221.3 MB.
Total: 14.229800 ms (FindLiveObjects: 1.204800 ms CreateObjectMapping: 0.853300 ms MarkObjects: 7.904200 ms  DeleteObjects: 4.265800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.42 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6474 unused Assets / (5.8 MB). Loaded Objects now: 7153.
Memory consumption went from 227.4 MB to 221.5 MB.
Total: 13.013300 ms (FindLiveObjects: 1.127900 ms CreateObjectMapping: 0.850500 ms MarkObjects: 6.727700 ms  DeleteObjects: 4.305300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.418 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.76 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.216 seconds
Domain Reload Profiling: 2634ms
	BeginReloadAssembly (324ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (74ms)
	RebuildCommonClasses (67ms)
	RebuildNativeTypeToScriptingClass (25ms)
	initialDomainReloadingComplete (57ms)
	LoadAllAssembliesAndSetupDomain (945ms)
		LoadAssemblies (685ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (435ms)
			TypeCache.Refresh (28ms)
				TypeCache.ScanAssembly (7ms)
			BuildScriptInfoCaches (381ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1217ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (987ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (28ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (184ms)
			ProcessInitializeOnLoadAttributes (658ms)
			ProcessInitializeOnLoadMethodAttributes (99ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.52 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6481 unused Assets / (5.7 MB). Loaded Objects now: 7155.
Memory consumption went from 227.2 MB to 221.5 MB.
Total: 15.992600 ms (FindLiveObjects: 1.398900 ms CreateObjectMapping: 0.861800 ms MarkObjects: 9.109100 ms  DeleteObjects: 4.621200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.424 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.36 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.224 seconds
Domain Reload Profiling: 2648ms
	BeginReloadAssembly (337ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (81ms)
	RebuildCommonClasses (68ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (50ms)
	LoadAllAssembliesAndSetupDomain (944ms)
		LoadAssemblies (698ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (426ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (385ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1225ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (990ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (21ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (182ms)
			ProcessInitializeOnLoadAttributes (665ms)
			ProcessInitializeOnLoadMethodAttributes (98ms)
			AfterProcessingInitializeOnLoad (18ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.55 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6481 unused Assets / (5.8 MB). Loaded Objects now: 7157.
Memory consumption went from 227.2 MB to 221.4 MB.
Total: 18.192300 ms (FindLiveObjects: 1.126700 ms CreateObjectMapping: 0.845700 ms MarkObjects: 10.214600 ms  DeleteObjects: 6.004000 ms)

Prepare: number of updated asset objects reloaded= 0
