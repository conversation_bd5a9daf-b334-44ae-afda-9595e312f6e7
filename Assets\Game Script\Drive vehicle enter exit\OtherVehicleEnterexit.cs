using UnityEngine;
using System.Collections;
using System.Reflection;

public class OtherVehicleEnterexit : MonoBehaviour
{
    [Header("Vehicle Tags")]
    public string helicopterTag = "Helicopter";
    public string airplaneTag = "Airplane";
    public string motorbikeTag = "Motorbike";
    public string boatTag = "Boat";
    public string trainTag = "Train";
    [Header("Position")]
    public Transform BoatPosition;
    [Header("Cameras")]
    public GameObject motorbikecam, helicoptercam, Playercam, airplanecam, traincam, boatcam;

    [Header("UI Canvas")]
    public GameObject helicoptercanvas, airplanecanvas, motorbikecanvas, boatcanvas, trainscanvas, Playercanvas;

    [Header("Player")]
    public Transform Player;

    [Header("Settings")]
    public float distance = 10f;
    public float motorbikeDistance = 4f; // Distance for motorbike
    public float maxExitSpeed = 5f; // Maximum speed to allow exit (in km/h)

    [Header("UI Buttons")]
    public GameObject Helicopterenterbutton, Helicopterexitbutton;
    public GameObject Airplaneenterbutton, Airplaneexitbutton;
    public GameObject Motorbikeenterbutton, Motorbikeexitbutton;
    public GameObject Boatenterbutton, Boatexitbutton;
    public GameObject Trainenterbutton, Trainexitbutton;

    // Private variables to track state
    private bool isInVehicle = false;
    private GameObject currentVehicle = null;
    private Transform originalParent;

    void Start()
    {
        // Hide all buttons at start
        HideAllButtons();
    }

    void Update()
    {
        if (!isInVehicle)
        {
            CheckVehicleDistances();
        }
        else
        {
            CheckExitConditions();
        }
    }

    private void HideAllButtons()
    {
        if (Helicopterenterbutton != null) Helicopterenterbutton.SetActive(false);
        if (Helicopterexitbutton != null) Helicopterexitbutton.SetActive(false);
        if (Airplaneenterbutton != null) Airplaneenterbutton.SetActive(false);
        if (Airplaneexitbutton != null) Airplaneexitbutton.SetActive(false);
        if (Motorbikeenterbutton != null) Motorbikeenterbutton.SetActive(false);
        if (Motorbikeexitbutton != null) Motorbikeexitbutton.SetActive(false);
        if (Boatenterbutton != null) Boatenterbutton.SetActive(false);
        if (Boatexitbutton != null) Boatexitbutton.SetActive(false);
        if (Trainenterbutton != null) Trainenterbutton.SetActive(false);
        if (Trainexitbutton != null) Trainexitbutton.SetActive(false);
    }

    private void CheckVehicleDistances()
    {
        // Check helicopter distance (find closest)
        GameObject closestHelicopter = FindClosestVehicleWithTag(helicopterTag, distance);
        if (closestHelicopter != null)
        {
            if (Helicopterenterbutton != null) Helicopterenterbutton.SetActive(true);
        }
        else
        {
            if (Helicopterenterbutton != null) Helicopterenterbutton.SetActive(false);
        }

        // Check airplane distance (find closest)
        GameObject closestAirplane = FindClosestVehicleWithTag(airplaneTag, distance);
        if (closestAirplane != null)
        {
            if (Airplaneenterbutton != null) Airplaneenterbutton.SetActive(true);
        }
        else
        {
            if (Airplaneenterbutton != null) Airplaneenterbutton.SetActive(false);
        }

        // Check motorbike distance (find closest)
        GameObject closestMotorbike = FindClosestVehicleWithTag(motorbikeTag, motorbikeDistance);
        if (closestMotorbike != null)
        {
            if (Motorbikeenterbutton != null) Motorbikeenterbutton.SetActive(true);
        }
        else
        {
            if (Motorbikeenterbutton != null) Motorbikeenterbutton.SetActive(false);
        }

        // Check boat distance (find closest)
        GameObject closestBoat = FindClosestVehicleWithTag(boatTag, distance);
        if (closestBoat != null)
        {
            if (Boatenterbutton != null) Boatenterbutton.SetActive(true);
        }
        else
        {
            if (Boatenterbutton != null) Boatenterbutton.SetActive(false);
        }

        // Check train distance (find closest)
        GameObject closestTrain = FindClosestVehicleWithTag(trainTag, distance);
        if (closestTrain != null)
        {
            if (Trainenterbutton != null) Trainenterbutton.SetActive(true);
        }
        else
        {
            if (Trainenterbutton != null) Trainenterbutton.SetActive(false);
        }
    }

    // Helper function to find closest vehicle with specific tag within distance
    private GameObject FindClosestVehicleWithTag(string tag, float maxDistance)
    {
        GameObject[] vehicles = GameObject.FindGameObjectsWithTag(tag);
        GameObject closestVehicle = null;
        float closestDistance = maxDistance;

        foreach (GameObject vehicle in vehicles)
        {
            float distance = Vector3.Distance(Player.position, vehicle.transform.position);
            if (distance <= closestDistance)
            {
                closestDistance = distance;
                closestVehicle = vehicle;
            }
        }

        return closestVehicle;
    }

    private void CheckExitConditions()
    {
        if (currentVehicle == null) return;

        float vehicleSpeed = 0f;

        // Get speed from rigidbody velocity (universal method)
        var vehicleRigidbody = currentVehicle.GetComponent<Rigidbody>();
        if (vehicleRigidbody != null)
        {
            // Convert m/s to km/h
            vehicleSpeed = vehicleRigidbody.linearVelocity.magnitude * 3.6f;
        }

        // Show exit button based on vehicle type and speed
        if (currentVehicle.CompareTag(helicopterTag))
        {
            // Helicopter can exit anytime (no speed restriction)
            if (Helicopterexitbutton != null) Helicopterexitbutton.SetActive(true);
        }
        else if (currentVehicle.CompareTag(airplaneTag))
        {
            if (Airplaneexitbutton != null) Airplaneexitbutton.SetActive(vehicleSpeed <= maxExitSpeed);
        }
        else if (currentVehicle.CompareTag(motorbikeTag))
        {
            if (Motorbikeexitbutton != null) Motorbikeexitbutton.SetActive(vehicleSpeed <= maxExitSpeed);
        }
        else if (currentVehicle.CompareTag(boatTag))
        {
            if (Boatexitbutton != null) Boatexitbutton.SetActive(vehicleSpeed <= maxExitSpeed);
        }
        else if (currentVehicle.CompareTag(trainTag))
        {
            if (Trainexitbutton != null) Trainexitbutton.SetActive(vehicleSpeed <= maxExitSpeed);
        }
    }

    // Helicopter Enter/Exit Functions
    public void EnterHelicopter()
    {
        GameObject helicopter = FindClosestVehicleWithTag(helicopterTag, distance);
        if (helicopter != null)
            EnterVehicle(helicopter, helicoptercam, helicoptercanvas);
    }

    public void ExitHelicopter()
    {
        if (currentVehicle != null && currentVehicle.CompareTag(helicopterTag))
            ExitVehicle(currentVehicle, helicoptercam, helicoptercanvas);
    }

    // Airplane Enter/Exit Functions
    public void EnterAirplane()
    {
        GameObject airplane = FindClosestVehicleWithTag(airplaneTag, distance);
        if (airplane != null)
            EnterVehicle(airplane, airplanecam, airplanecanvas);
    }

    public void ExitAirplane()
    {
        if (currentVehicle != null && currentVehicle.CompareTag(airplaneTag))
            ExitVehicle(currentVehicle, airplanecam, airplanecanvas);
    }

    // Motorbike Enter/Exit Functions
    public void EnterMotorbike()
    {
        GameObject motorbike = FindClosestVehicleWithTag(motorbikeTag, motorbikeDistance);
        if (motorbike != null)
            EnterVehicle(motorbike, motorbikecam, motorbikecanvas);
    }

    public void ExitMotorbike()
    {
        if (currentVehicle != null && currentVehicle.CompareTag(motorbikeTag))
            ExitVehicle(currentVehicle, motorbikecam, motorbikecanvas);
    }

    // Boat Enter/Exit Functions
    public void EnterBoat()
    {
        GameObject boat = FindClosestVehicleWithTag(boatTag, distance);
        if (boat != null)
            EnterVehicle(boat, boatcam, boatcanvas);
    }

    public void ExitBoat()
    {
        if (currentVehicle != null && currentVehicle.CompareTag(boatTag))
        {
            StartCoroutine(ExitBoatWithDelay());
        }
    }

    private IEnumerator ExitBoatWithDelay()
    {
        // Set boat position first
        GameObject boat = GameObject.FindGameObjectWithTag(boatTag);
        if (boat != null && BoatPosition != null)
        {
            boat.transform.position = BoatPosition.position;
            boat.transform.rotation = BoatPosition.rotation;
        }

        // Wait 0.5 seconds before unparenting player
        yield return new WaitForSeconds(0.5f);

        // Then exit the vehicle (unparent player)
        ExitVehicle(currentVehicle, boatcam, boatcanvas);
    }

    // Train Enter/Exit Functions
    public void EnterTrain()
    {
        GameObject train = FindClosestVehicleWithTag(trainTag, distance);
        if (train != null)
            EnterVehicle(train, traincam, trainscanvas);
    }

    public void ExitTrain()
    {
        if (currentVehicle != null && currentVehicle.CompareTag(trainTag))
            ExitVehicle(currentVehicle, traincam, trainscanvas);
    }

    // Generic Enter Vehicle Function
    private void EnterVehicle(GameObject vehicle, GameObject vehicleCamera, GameObject vehicleCanvas)
    {
        if (vehicle == null) return;

        // Store original parent before parenting to vehicle
        originalParent = Player.parent;
        currentVehicle = vehicle;

        // Switch cameras and UI
        if (Playercam != null) Playercam.SetActive(false);
        if (Player != null) Player.gameObject.SetActive(false);
        if (vehicleCamera != null) vehicleCamera.SetActive(true);
        if (Playercanvas != null) Playercanvas.SetActive(false);
        if (vehicleCanvas != null) vehicleCanvas.SetActive(true);

        // Enable vehicle engine based on vehicle type
        EnableVehicleEngine(vehicle, true);

        // Enable any vehicle controller (generic approach)
        var vehicleController = vehicle.GetComponent<MonoBehaviour>();
        if (vehicleController != null)
        {
            vehicleController.enabled = true;
        }


        // Parent player to vehicle and set position
        if (Player != null)
        {
            Player.SetParent(vehicle.transform);
            var playerPosition = vehicle.GetComponent<VehiclePlayerPosition>();
            if (playerPosition != null && playerPosition.SeatPosition != null)
            {
                Player.position = playerPosition.SeatPosition.position;
            }
        }

        // Hide all enter buttons and set vehicle state
        HideAllButtons();
        isInVehicle = true;
    }

    // Generic Exit Vehicle Function
    private void ExitVehicle(GameObject vehicle, GameObject vehicleCamera, GameObject vehicleCanvas)
    {
        if (vehicle == null || !isInVehicle) return;

        // Unparent player from vehicle first
        if (Player != null) Player.SetParent(originalParent);

        // Switch cameras and UI
        if (Playercam != null) Playercam.SetActive(true);
        if (Player != null) Player.gameObject.SetActive(true);
        if (vehicleCamera != null) vehicleCamera.SetActive(false);
        if (Playercanvas != null) Playercanvas.SetActive(true);
        if (vehicleCanvas != null) vehicleCanvas.SetActive(false);

        // Set player position to door position (now unparented, so it's relative to world)
        var playerPosition = vehicle.GetComponent<VehiclePlayerPosition>();
        if (playerPosition != null && playerPosition.DoorPosition != null && Player != null)
        {
            Player.position = playerPosition.DoorPosition.position;
        }

        // Position camera behind the character when exiting vehicle
        StartCoroutine(PositionCameraBehindPlayer());

        // Disable vehicle engine
        EnableVehicleEngine(vehicle, false);

        // Handle vehicle rigidbody for stopping
       

        // Disable vehicle controller after delay
        StartCoroutine(DisableVehicleAfterDelay(vehicle));

        // Reset state
        currentVehicle = null;
        isInVehicle = false;
        HideAllButtons();
    }

    private IEnumerator DisableVehicleAfterDelay(GameObject vehicle)
    {
        yield return new WaitForSeconds(1f);

        // Disable any vehicle controller components
        var vehicleController = vehicle.GetComponent<MonoBehaviour>();
        if (vehicleController != null)
        {
            vehicleController.enabled = false;
        }
    }

    // Enable/Disable vehicle engines using reflection to avoid type dependencies
    private void EnableVehicleEngine(GameObject vehicle, bool enable)
    {
        // Get all MonoBehaviour components on the vehicle
        var components = vehicle.GetComponents<MonoBehaviour>();

        foreach (var component in components)
        {
            if (component == null) continue;

            var componentType = component.GetType();
            var componentName = componentType.Name;

            // Handle different vehicle controllers by name
            switch (componentName)
            {
                case "HelicopterController":
                    // Set EngineForce property
                    var engineForceProperty = componentType.GetProperty("EngineForce");
                    if (engineForceProperty != null)
                    {
                        engineForceProperty.SetValue(component, enable ? 10f : 0f);
                    }
                    break;

                case "TrainController_v3":
                    // Set EnginesOn property
                    var enginesOnProperty = componentType.GetProperty("EnginesOn");
                    if (enginesOnProperty != null)
                    {
                        enginesOnProperty.SetValue(component, enable);
                    }
                    break;

                case "BikeControl":
                    // Enable/disable the component and control engine
                    component.enabled = enable;
                    if (enable)
                    {
                        // Set activeControl to true for bike input to work
                        var activeControlField = componentType.GetField("activeControl");
                        if (activeControlField != null)
                        {
                            activeControlField.SetValue(component, true);
                        }

                        // Set control mode to touch for mobile controls
                        var controlModeField = componentType.GetField("controlMode");
                        if (controlModeField != null)
                        {
                            // Get the ControlMode enum type and set to touch (value 2)
                            var controlModeType = controlModeField.FieldType;
                            var touchMode = System.Enum.ToObject(controlModeType, 2); // ControlMode.touch = 2
                            controlModeField.SetValue(component, touchMode);
                        }

                        // Start the bike engine when entering
                        var startEngineMethod = componentType.GetMethod("StartEngine");
                        if (startEngineMethod != null)
                        {
                            startEngineMethod.Invoke(component, null);
                        }

                        // Set active bike in UI controller for touch controls
                        var bikeUIManager = FindObjectOfType<BikeUIManager>();
                        if (bikeUIManager != null)
                        {
                            bikeUIManager.SetActiveBike((BikeControl)component);
                        }
                        else
                        {
                            // Fallback: try to find BikeUIController directly
                            var bikeUIController = FindObjectOfType<BikeUIController>();
                            if (bikeUIController != null)
                            {
                                bikeUIController.SetActiveBike((BikeControl)component);
                            }
                        }
                    }
                    else
                    {
                        // Set activeControl to false when exiting
                        var activeControlField = componentType.GetField("activeControl");
                        if (activeControlField != null)
                        {
                            activeControlField.SetValue(component, false);
                        }

                        // Stop the bike engine when exiting
                        var stopEngineMethod = componentType.GetMethod("StopEngine");
                        if (stopEngineMethod != null)
                        {
                            stopEngineMethod.Invoke(component, null);
                        }

                        // Clear active bike in UI controller
                        var bikeUIManager = FindObjectOfType<BikeUIManager>();
                        if (bikeUIManager != null)
                        {
                            bikeUIManager.ClearActiveBike();
                        }
                        else
                        {
                            // Fallback: try to find BikeUIController directly
                            var bikeUIController = FindObjectOfType<BikeUIController>();
                            if (bikeUIController != null)
                            {
                                bikeUIController.SetActiveBike(null);
                            }
                        }
                    }
                    break;

                case "BoatController":
                    // Enable/disable the component
                    component.enabled = enable;
                    break;
            }
        }
    }

    private IEnumerator PositionCameraBehindPlayer()
    {
        yield return new WaitForSeconds(0.1f); // Small delay to ensure camera is active

        if (Playercam != null && Player != null)
        {
            // Try to get the vThirdPersonCamera component
            vThirdPersonCamera tpCamera = Playercam.GetComponent<vThirdPersonCamera>();
            if (tpCamera != null)
            {
                // Use reflection to access private mouseX and mouseY fields for back camera positioning
                var mouseXField = typeof(vThirdPersonCamera).GetField("mouseX", BindingFlags.NonPublic | BindingFlags.Instance);
                var mouseYField = typeof(vThirdPersonCamera).GetField("mouseY", BindingFlags.NonPublic | BindingFlags.Instance);

                if (mouseXField != null && mouseYField != null)
                {
                    // Set camera behind the player (Y rotation = player's Y rotation)
                    mouseXField.SetValue(tpCamera, Player.eulerAngles.y);
                    mouseYField.SetValue(tpCamera, 0f); // Level camera (no up/down angle)
                }
            }
            else
            {
                // Fallback: Calculate position behind the player
                Vector3 behindPosition = Player.position - Player.forward * 3f + Vector3.up * 1.5f;
                Playercam.transform.position = behindPosition;

                // Make camera look at the player
                Vector3 lookDirection = Player.position + Vector3.up * 1.5f - Playercam.transform.position;
                if (lookDirection != Vector3.zero)
                {
                    Playercam.transform.rotation = Quaternion.LookRotation(lookDirection);
                }
            }
        }
    }
}
